defmodule Repobot.Workers.RefreshFileContentWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  alias Repobot.Workers.RefreshFileContentWorker
  alias Repobot.{RepositoryFiles, Repo}

  import Repobot.Test.Fixtures
  import Mox

  setup do
    user = user_fixture()
    organization = user.default_organization
    repository = create_repository(%{organization_id: organization.id})

    # Create some repository files
    file1 = create_repository_file(%{repository: repository, path: "README.md", content: nil})
    file2 = create_repository_file(%{repository: repository, path: "package.json", content: nil})

    %{user: user, organization: organization, repository: repository, files: [file1, file2]}
  end

  describe "perform/1" do
    test "successfully refreshes file content with component name", %{
      user: user,
      repository: repository,
      files: files
    } do
      # Preload files for the repository
      repository = Repo.preload(repository, :files, force: true)

      # Mock the GitHub API calls
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, 2, fn _, _, _, _ ->
        {:ok, "file content", %{}}
      end)

      # Create job args with component name
      job_args = %{
        "repository_ids" => [repository.id],
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files",
        "job_id" => "test-job-123"
      }

      assert :ok = perform_job(RefreshFileContentWorker, job_args)

      # Should receive progress updates and completion message
      assert_receive {:repo_tree, :template_files, {:content_refresh_progress, progress, _status}}
      assert is_integer(progress) and progress >= 0 and progress <= 100

      assert_receive {:repo_tree, :template_files, {:content_refresh_complete, refreshed_repos}}
      assert length(refreshed_repos) == 1

      # Verify files were updated with content
      updated_files = Repo.preload(List.first(refreshed_repos), :files).files
      assert Enum.all?(updated_files, &(&1.content == "file content"))
    end

    test "successfully refreshes file content without component name", %{
      user: user,
      repository: repository
    } do
      # Preload files for the repository
      repository = Repo.preload(repository, :files, force: true)

      # Mock the GitHub API calls
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, 2, fn _, _, _, _ ->
        {:ok, "file content", %{}}
      end)

      # Create job args without component name
      job_args = %{
        "repository_ids" => [repository.id],
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "job_id" => "test-job-123"
      }

      assert :ok = perform_job(RefreshFileContentWorker, job_args)

      # Should receive progress updates and completion message directly
      assert_receive {:content_refresh_progress, progress, _status}
      assert is_integer(progress) and progress >= 0 and progress <= 100

      assert_receive {:content_refresh_complete, refreshed_repos}
      assert length(refreshed_repos) == 1
    end

    test "handles empty file list", %{user: user, repository: repository} do
      # Create repository with no files needing refresh
      repository = Repo.preload(repository, :files, force: true)

      # Update all files to have content so none need refresh
      Enum.each(repository.files, fn file ->
        RepositoryFiles.update_repository_file(file, %{content: "existing content"})
      end)

      repository = Repo.preload(repository, :files, force: true)

      job_args = %{
        "repository_ids" => [repository.id],
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files",
        "job_id" => "test-job-123"
      }

      assert :ok = perform_job(RefreshFileContentWorker, job_args)

      # Should receive completion message immediately
      assert_receive {:repo_tree, :template_files, {:content_refresh_complete, refreshed_repos}}
      assert length(refreshed_repos) == 1
    end

    test "handles GitHub API errors gracefully", %{user: user, repository: repository} do
      # Preload files for the repository
      repository = Repo.preload(repository, :files, force: true)

      # Mock the GitHub API calls to return errors
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, 2, fn _, _, _, _ ->
        {:error, "API rate limit exceeded"}
      end)

      job_args = %{
        "repository_ids" => [repository.id],
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files",
        "job_id" => "test-job-123"
      }

      assert :ok = perform_job(RefreshFileContentWorker, job_args)

      # Should still receive completion message even with errors
      assert_receive {:repo_tree, :template_files, {:content_refresh_complete, refreshed_repos}}
      assert length(refreshed_repos) == 1
    end

    test "handles exception during processing", %{user: user, repository: repository} do
      # Preload files for the repository
      repository = Repo.preload(repository, :files, force: true)

      # Mock the GitHub API calls to raise an exception
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_file_content, fn _, _, _, _ ->
        raise "Unexpected error"
      end)

      job_args = %{
        "repository_ids" => [repository.id],
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files",
        "job_id" => "test-job-123"
      }

      assert {:error, "Unexpected error"} = perform_job(RefreshFileContentWorker, job_args)

      # Should receive error message
      assert_receive {:repo_tree, :template_files, {:content_refresh_error, "Unexpected error"}}
    end

    test "handles missing user", %{repository: repository} do
      non_existent_id = -1

      job_args = %{
        "repository_ids" => [repository.id],
        "user_id" => non_existent_id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "job_id" => "test-job-123"
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(RefreshFileContentWorker, job_args)
      end
    end

    test "handles missing repository", %{user: user} do
      non_existent_id = -1

      job_args = %{
        "repository_ids" => [non_existent_id],
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "job_id" => "test-job-123"
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(RefreshFileContentWorker, job_args)
      end
    end
  end
end
