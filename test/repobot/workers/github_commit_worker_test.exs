defmodule Repobot.Workers.GitHubCommitWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  alias Repobot.Workers.GitHubCommitWorker

  import Repobot.Test.Fixtures

  setup do
    user = user_fixture()
    organization = organization_fixture(user)

    template_repo =
      repository_fixture(organization, %{
        template: true,
        data: %{"default_branch" => "main"}
      })

    source_files = [
      %{
        "target_path" => "README.md",
        "content" => "# Test Project\nThis is a test."
      },
      %{
        "target_path" => "package.json",
        "content" => "{\"name\": \"test-project\"}"
      }
    ]

    %{
      user: user,
      organization: organization,
      template_repo: template_repo,
      source_files: source_files
    }
  end

  describe "perform/1" do
    test "successfully commits files to GitHub", %{
      user: user,
      template_repo: template_repo,
      source_files: source_files
    } do
      # Mock GitHub API calls for successful commit
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_ref, fn _, _, _, _ ->
        {200, %{"object" => %{"sha" => "parent_sha_123"}}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_tree, fn _, _, _, _, _ ->
        {201, %{"sha" => "tree_sha_456"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_commit, fn _, _, _, _, _, _ ->
        {201, %{"sha" => "commit_sha_789"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :update_reference, fn _, _, _, _, _, _ ->
        {200, %{}, %{}}
      end)

      # Mock repository refresh
      expect(Repobot.Test.GitHubMock, :refresh_repository_files!, fn _, _ ->
        {:ok, template_repo}
      end)

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(GitHubCommitWorker, job_args)

      # Should receive success message
      assert_receive {:github_commit_result, :ok, returned_source_files, returned_template_repo}
      assert returned_source_files == source_files
      assert returned_template_repo.id == template_repo.id
    end

    test "handles empty source files list", %{
      user: user,
      template_repo: template_repo
    } do
      job_args = %{
        "source_files" => [],
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(GitHubCommitWorker, job_args)

      # Should receive success message immediately (no commit needed)
      assert_receive {:github_commit_result, :ok, [], returned_template_repo}
      assert returned_template_repo.id == template_repo.id
    end

    test "handles source files with nil content", %{
      user: user,
      template_repo: template_repo
    } do
      source_files_with_nil = [
        %{
          "target_path" => "README.md",
          "content" => nil
        }
      ]

      job_args = %{
        "source_files" => source_files_with_nil,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(GitHubCommitWorker, job_args)

      # Should receive success message (files with nil content are filtered out)
      assert_receive {:github_commit_result, :ok, returned_source_files, _template_repo}
      assert returned_source_files == source_files_with_nil
    end

    test "handles GitHub API error during get_reference", %{
      user: user,
      template_repo: template_repo,
      source_files: source_files
    } do
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_ref, fn _, _, _, _ ->
        {404, %{"message" => "Not Found"}, %{}}
      end)

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert {:error, _reason} = perform_job(GitHubCommitWorker, job_args)

      # Should receive error message
      assert_receive {:github_commit_result, {:error, reason}, returned_source_files,
                      returned_template_repo}

      assert String.contains?(reason, "Failed to get reference")
      assert returned_source_files == source_files
      assert returned_template_repo.id == template_repo.id
    end

    test "handles GitHub API error during create_tree", %{
      user: user,
      template_repo: template_repo,
      source_files: source_files
    } do
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_ref, fn _, _, _, _ ->
        {200, %{"object" => %{"sha" => "parent_sha_123"}}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_tree, fn _, _, _, _, _ ->
        {422, %{"message" => "Validation Failed"}, %{}}
      end)

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert {:error, _reason} = perform_job(GitHubCommitWorker, job_args)

      # Should receive error message
      assert_receive {:github_commit_result, {:error, reason}, _source_files, _template_repo}
      assert String.contains?(reason, "Failed to create tree")
    end

    test "handles GitHub API error during create_commit", %{
      user: user,
      template_repo: template_repo,
      source_files: source_files
    } do
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_ref, fn _, _, _, _ ->
        {200, %{"object" => %{"sha" => "parent_sha_123"}}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_tree, fn _, _, _, _, _ ->
        {201, %{"sha" => "tree_sha_456"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_commit, fn _, _, _, _, _, _ ->
        {422, %{"message" => "Validation Failed"}, %{}}
      end)

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert {:error, _reason} = perform_job(GitHubCommitWorker, job_args)

      # Should receive error message
      assert_receive {:github_commit_result, {:error, reason}, _source_files, _template_repo}
      assert String.contains?(reason, "Failed to create commit")
    end

    test "handles GitHub API error during update_reference", %{
      user: user,
      template_repo: template_repo,
      source_files: source_files
    } do
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_ref, fn _, _, _, _ ->
        {200, %{"object" => %{"sha" => "parent_sha_123"}}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_tree, fn _, _, _, _, _ ->
        {201, %{"sha" => "tree_sha_456"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_commit, fn _, _, _, _, _, _ ->
        {201, %{"sha" => "commit_sha_789"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :update_reference, fn _, _, _, _, _, _ ->
        {422, %{"message" => "Validation Failed"}, %{}}
      end)

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert {:error, _reason} = perform_job(GitHubCommitWorker, job_args)

      # Should receive error message
      assert_receive {:github_commit_result, {:error, reason}, _source_files, _template_repo}
      assert String.contains?(reason, "Failed to update ref")
    end

    test "handles repository refresh failure", %{
      user: user,
      template_repo: template_repo,
      source_files: source_files
    } do
      # Mock successful GitHub API calls
      expect(Repobot.Test.GitHubMock, :client, fn _, _ -> :mock_client end)

      expect(Repobot.Test.GitHubMock, :get_ref, fn _, _, _, _ ->
        {200, %{"object" => %{"sha" => "parent_sha_123"}}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_tree, fn _, _, _, _, _ ->
        {201, %{"sha" => "tree_sha_456"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :create_commit, fn _, _, _, _, _, _ ->
        {201, %{"sha" => "commit_sha_789"}, %{}}
      end)

      expect(Repobot.Test.GitHubMock, :update_reference, fn _, _, _, _, _, _ ->
        {200, %{}, %{}}
      end)

      # Mock repository refresh failure
      expect(Repobot.Test.GitHubMock, :refresh_repository_files!, fn _, _ ->
        {:error, "API rate limit exceeded"}
      end)

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(GitHubCommitWorker, job_args)

      # Should still receive success message (refresh failure is logged but not fatal)
      assert_receive {:github_commit_result, :ok, returned_source_files, returned_template_repo}
      assert returned_source_files == source_files
      assert returned_template_repo.id == template_repo.id
    end

    test "handles missing template repository", %{
      user: user,
      source_files: source_files
    } do
      non_existent_id = -1

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => non_existent_id,
        "user_id" => user.id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(GitHubCommitWorker, job_args)
      end
    end

    test "handles missing user", %{
      template_repo: template_repo,
      source_files: source_files
    } do
      non_existent_id = -1

      job_args = %{
        "source_files" => source_files,
        "template_repo_id" => template_repo.id,
        "user_id" => non_existent_id,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(GitHubCommitWorker, job_args)
      end
    end
  end
end
