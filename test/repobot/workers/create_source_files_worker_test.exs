defmodule Repobot.Workers.CreateSourceFilesWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  alias Repobot.Workers.CreateSourceFilesWorker
  alias Repobot.{SourceFiles, Repo}

  import Repobot.Test.Fixtures

  setup do
    user = user_fixture()
    organization = organization_fixture(user)
    template_repo = repository_fixture(organization, %{template: true})
    target_repo = repository_fixture(organization, %{name: "target-repo"})
    folder = folder_fixture(organization)
    
    # Associate target repo with folder
    {:ok, _} = Repobot.Repositories.update_repository(target_repo, %{folder_id: folder.id})
    target_repo = Repo.get!(Repobot.Repository, target_repo.id)
    
    selected_files = [
      %{
        "path" => "README.md",
        "content" => "# Test Project\nThis is a test."
      },
      %{
        "path" => "package.json",
        "content" => "{\"name\": \"test-project\"}"
      }
    ]
    
    %{
      user: user,
      organization: organization,
      template_repo: template_repo,
      target_repo: target_repo,
      folder: folder,
      selected_files: selected_files
    }
  end

  describe "perform/1" do
    test "successfully creates source files", %{
      user: user,
      template_repo: template_repo,
      folder: folder,
      selected_files: selected_files
    } do
      job_args = %{
        "selected_files" => selected_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "target_folder_ids" => [folder.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(CreateSourceFilesWorker, job_args)

      # Should receive success message with created source files
      assert_receive {:create_source_files, newly_created_source_files, returned_template_repo}
      
      assert length(newly_created_source_files) == 2
      assert returned_template_repo.id == template_repo.id
      
      # Verify source files were created in database
      source_files = SourceFiles.list_source_files(user, user.default_organization)
      assert length(source_files) == 2
      
      readme_file = Enum.find(source_files, &(&1.target_path == "README.md"))
      package_file = Enum.find(source_files, &(&1.target_path == "package.json"))
      
      assert readme_file.content == "# Test Project\nThis is a test."
      assert package_file.content == "{\"name\": \"test-project\"}"
      assert readme_file.source_repository_id == template_repo.id
      assert package_file.source_repository_id == template_repo.id
    end

    test "handles empty selected files list", %{
      user: user,
      template_repo: template_repo,
      folder: folder
    } do
      job_args = %{
        "selected_files" => [],
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "target_folder_ids" => [folder.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(CreateSourceFilesWorker, job_args)

      # Should receive success message with empty list
      assert_receive {:create_source_files, [], returned_template_repo}
      assert returned_template_repo.id == template_repo.id
    end

    test "handles source file creation failure", %{
      user: user,
      template_repo: template_repo,
      folder: folder
    } do
      # Create invalid selected files (missing required fields)
      invalid_selected_files = [
        %{
          "path" => "",  # Empty path should cause validation error
          "content" => "test content"
        }
      ]

      job_args = %{
        "selected_files" => invalid_selected_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "target_folder_ids" => [folder.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert {:error, _reason} = perform_job(CreateSourceFilesWorker, job_args)

      # Should receive error message
      assert_receive {:create_source_files_error, _reason}
    end

    test "associates source files with multiple folders", %{
      user: user,
      organization: organization,
      template_repo: template_repo,
      folder: folder,
      selected_files: selected_files
    } do
      # Create another folder
      folder2 = folder_fixture(organization, %{name: "folder2"})
      
      job_args = %{
        "selected_files" => selected_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "target_folder_ids" => [folder.id, folder2.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(CreateSourceFilesWorker, job_args)

      assert_receive {:create_source_files, newly_created_source_files, _template_repo}
      
      # Verify source files are associated with both folders
      source_file = List.first(newly_created_source_files)
      source_file_with_folders = Repo.preload(source_file, :folders)
      
      folder_ids = Enum.map(source_file_with_folders.folders, & &1.id)
      assert folder.id in folder_ids
      assert folder2.id in folder_ids
    end

    test "handles missing template repository", %{
      user: user,
      folder: folder,
      selected_files: selected_files
    } do
      non_existent_id = -1

      job_args = %{
        "selected_files" => selected_files,
        "template_repo_id" => non_existent_id,
        "user_id" => user.id,
        "target_folder_ids" => [folder.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(CreateSourceFilesWorker, job_args)
      end
    end

    test "handles missing user", %{
      template_repo: template_repo,
      folder: folder,
      selected_files: selected_files
    } do
      non_existent_id = -1

      job_args = %{
        "selected_files" => selected_files,
        "template_repo_id" => template_repo.id,
        "user_id" => non_existent_id,
        "target_folder_ids" => [folder.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(CreateSourceFilesWorker, job_args)
      end
    end

    test "handles missing folder", %{
      user: user,
      template_repo: template_repo,
      selected_files: selected_files
    } do
      non_existent_id = -1

      job_args = %{
        "selected_files" => selected_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "target_folder_ids" => [non_existent_id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(CreateSourceFilesWorker, job_args)
      end
    end

    test "sets correct attributes for source files", %{
      user: user,
      template_repo: template_repo,
      folder: folder,
      selected_files: selected_files
    } do
      job_args = %{
        "selected_files" => selected_files,
        "template_repo_id" => template_repo.id,
        "user_id" => user.id,
        "target_folder_ids" => [folder.id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(CreateSourceFilesWorker, job_args)

      assert_receive {:create_source_files, newly_created_source_files, _template_repo}
      
      source_file = List.first(newly_created_source_files)
      
      assert source_file.organization_id == user.default_organization_id
      assert source_file.source_repository_id == template_repo.id
      assert source_file.read_only == template_repo.template
      assert source_file.user_id == user.id
      assert source_file.name == Path.basename(source_file.target_path)
    end
  end
end
