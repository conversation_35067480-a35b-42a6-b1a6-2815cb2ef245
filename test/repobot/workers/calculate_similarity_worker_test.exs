defmodule Repobot.Workers.CalculateSimilarityWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  alias Repobot.Workers.CalculateSimilarityWorker
  alias Repobot.Repo

  import Repobot.Test.Fixtures

  setup do
    user = user_fixture()
    organization = organization_fixture(user)
    repository1 = repository_fixture(organization, %{name: "repo1"})
    repository2 = repository_fixture(organization, %{name: "repo2"})
    
    # Create repository files with content
    file1_repo1 = repository_file_fixture(repository1, %{
      path: "README.md", 
      content: "# Project\nThis is a test project.",
      type: "file"
    })
    file1_repo2 = repository_file_fixture(repository2, %{
      path: "README.md", 
      content: "# Project\nThis is a test project.",
      type: "file"
    })
    
    file2_repo1 = repository_file_fixture(repository1, %{
      path: "package.json", 
      content: "{\"name\": \"test\"}",
      type: "file"
    })
    file2_repo2 = repository_file_fixture(repository2, %{
      path: "package.json", 
      content: "{\"name\": \"different\"}",
      type: "file"
    })
    
    # Preload files
    repository1 = Repo.preload(repository1, :files, force: true)
    repository2 = Repo.preload(repository2, :files, force: true)
    
    common_files = [
      %{
        "path" => "README.md",
        "count" => 2,
        "size" => 100
      },
      %{
        "path" => "package.json",
        "count" => 2,
        "size" => 50
      }
    ]
    
    %{
      user: user, 
      organization: organization, 
      repositories: [repository1, repository2],
      common_files: common_files
    }
  end

  describe "perform/1" do
    test "successfully calculates similarity with component name", %{repositories: repositories, common_files: common_files} do
      repository_ids = Enum.map(repositories, & &1.id)

      job_args = %{
        "common_files" => common_files,
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      assert :ok = perform_job(CalculateSimilarityWorker, job_args)

      # Should receive progress updates and completion message
      assert_receive {:files, :template_files, {:similarity_progress, progress}}
      assert is_integer(progress) and progress >= 0 and progress <= 100

      assert_receive {:files, :template_files, {:similarity_complete, files_with_similarity}}
      assert length(files_with_similarity) == 2
      
      # Check that similarity scores were calculated
      readme_file = Enum.find(files_with_similarity, &(&1["path"] == "README.md"))
      package_file = Enum.find(files_with_similarity, &(&1["path"] == "package.json"))
      
      assert readme_file["similarity"] == 100  # Identical content
      assert package_file["similarity"] < 100  # Different content
      assert is_binary(readme_file["content"])
      assert is_binary(package_file["content"])
    end

    test "successfully calculates similarity without component name", %{repositories: repositories, common_files: common_files} do
      repository_ids = Enum.map(repositories, & &1.id)

      job_args = %{
        "common_files" => common_files,
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self()))
      }

      assert :ok = perform_job(CalculateSimilarityWorker, job_args)

      # Should receive progress updates and completion message directly
      assert_receive {:similarity_progress, progress}
      assert is_integer(progress) and progress >= 0 and progress <= 100

      assert_receive {:similarity_complete, files_with_similarity}
      assert length(files_with_similarity) == 2
    end

    test "handles empty common files list", %{repositories: repositories} do
      repository_ids = Enum.map(repositories, & &1.id)

      job_args = %{
        "common_files" => [],
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      assert :ok = perform_job(CalculateSimilarityWorker, job_args)

      # Should receive completion message immediately with empty list
      assert_receive {:files, :template_files, {:similarity_complete, []}}
    end

    test "handles files with nil content", %{repositories: repositories} do
      repository_ids = Enum.map(repositories, & &1.id)
      
      # Create a common file that exists but has nil content in all repos
      common_files = [
        %{
          "path" => "nonexistent.txt",
          "count" => 2,
          "size" => 0
        }
      ]

      job_args = %{
        "common_files" => common_files,
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      assert :ok = perform_job(CalculateSimilarityWorker, job_args)

      # Should receive completion message with empty results (files with nil content are skipped)
      assert_receive {:files, :template_files, {:similarity_complete, []}}
    end

    test "handles single repository (100% similarity)", %{repositories: repositories, common_files: common_files} do
      # Use only one repository
      repository_ids = [List.first(repositories).id]

      job_args = %{
        "common_files" => common_files,
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      assert :ok = perform_job(CalculateSimilarityWorker, job_args)

      assert_receive {:files, :template_files, {:similarity_complete, files_with_similarity}}
      
      # All files should have 100% similarity since there's only one repo
      assert Enum.all?(files_with_similarity, &(&1["similarity"] == 100))
    end

    test "handles exception during processing", %{repositories: repositories, common_files: common_files} do
      repository_ids = Enum.map(repositories, & &1.id)
      
      # Create malformed common files that might cause an exception
      malformed_common_files = [
        %{
          "path" => nil,  # This might cause an issue
          "count" => 2,
          "size" => 100
        }
      ]

      job_args = %{
        "common_files" => malformed_common_files,
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      # The worker should handle the exception gracefully
      assert {:error, _reason} = perform_job(CalculateSimilarityWorker, job_args)

      # Should receive error message
      assert_receive {:files, :template_files, {:similarity_error, _reason}}
    end

    test "handles missing repository", %{common_files: common_files} do
      non_existent_id = -1

      job_args = %{
        "common_files" => common_files,
        "repository_ids" => [non_existent_id],
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      assert_raise Ecto.NoResultsError, fn ->
        perform_job(CalculateSimilarityWorker, job_args)
      end
    end

    test "sorts results by similarity, count, and path", %{repositories: repositories} do
      repository_ids = Enum.map(repositories, & &1.id)
      
      # Create files with different similarities and counts
      common_files = [
        %{"path" => "z_file.txt", "count" => 1, "size" => 100},
        %{"path" => "a_file.txt", "count" => 2, "size" => 100},
        %{"path" => "README.md", "count" => 2, "size" => 100},
        %{"path" => "package.json", "count" => 2, "size" => 50}
      ]

      job_args = %{
        "common_files" => common_files,
        "repository_ids" => repository_ids,
        "receiver_pid" => Base.encode64(:erlang.term_to_binary(self())),
        "component_name" => "template_files"
      }

      assert :ok = perform_job(CalculateSimilarityWorker, job_args)

      assert_receive {:files, :template_files, {:similarity_complete, files_with_similarity}}
      
      # Results should be sorted by similarity (desc), count (desc), path (asc)
      similarities = Enum.map(files_with_similarity, &{&1["similarity"], &1["count"], &1["path"]})
      sorted_similarities = Enum.sort_by(similarities, &{-elem(&1, 0), -elem(&1, 1), elem(&1, 2)})
      
      assert similarities == sorted_similarities
    end
  end
end
