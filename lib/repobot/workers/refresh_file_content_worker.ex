defmodule Repobot.Workers.RefreshFileContentWorker do
  @moduledoc """
  Oban worker for refreshing file content from GitHub asynchronously.

  This worker replaces the Task.async_stream calls in RepoTree.refresh_file_content/3
  and provides better reliability, retry logic, and observability.
  """

  use Oban.Worker, queue: :files, max_attempts: 3

  require Logger

  alias Repobot.{Repo, RepositoryFiles}

  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "repository_ids" => repository_ids,
          "user_id" => user_id,
          "receiver_pid" => receiver_pid,
          "component_name" => component_name,
          "job_id" => job_id
        }
      }) do
    user = Repo.get!(Repobot.Accounts.User, user_id)

    # Load repositories with files preloaded
    repositories =
      repository_ids
      |> Enum.map(&Repo.get!(Repobot.Repository, &1))
      |> Enum.map(&Repo.preload(&1, :files, force: true))

    # Get all files that need content refresh
    files_to_refresh =
      repositories
      |> Enum.flat_map(fn repo ->
        case repo.files do
          %Ecto.Association.NotLoaded{} ->
            Logger.error(
              "Repository #{repo.full_name} files not preloaded in RefreshFileContentWorker"
            )

            []

          files when is_list(files) ->
            files
        end
      end)
      |> Enum.filter(&is_nil(&1.content))

    total_files = length(files_to_refresh)

    if total_files == 0 do
      # No files to refresh, send completion immediately
      refreshed_repos = Enum.map(repositories, &Repo.preload(&1, :files, force: true))
      send_to_receiver(receiver_pid, component_name, {:content_refresh_complete, refreshed_repos})
      :ok
    else
      # Process files and send progress updates
      process_files(
        files_to_refresh,
        repositories,
        user,
        receiver_pid,
        component_name,
        job_id,
        total_files
      )
    end
  end

  defp process_files(
         files_to_refresh,
         repositories,
         _user,
         receiver_pid,
         component_name,
         _job_id,
         total_files
       ) do
    github_api = Application.get_env(:repobot, :github_api)

    try do
      # Process files in batches to avoid overwhelming the system
      files_to_refresh
      |> Enum.with_index()
      |> Enum.each(fn {file, index} ->
        repo = Enum.find(repositories, &(&1.id == file.repository_id))
        github_client = github_api.client(repo.owner, repo.name)

        case github_api.get_file_content(github_client, repo.owner, repo.name, file.path) do
          {:ok, content, _response} ->
            {:ok, _} =
              RepositoryFiles.update_repository_file(file, %{
                content: content,
                content_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
              })

            # Send progress update
            progress = trunc((index + 1) * 100 / total_files)

            send_to_receiver(
              receiver_pid,
              component_name,
              {:content_refresh_progress, progress, "Refreshing file content..."}
            )

          {:error, reason} ->
            Logger.warning(
              "Failed to refresh content for #{file.path} in #{repo.full_name}: #{inspect(reason)}"
            )
        end
      end)

      # Send completion message
      refreshed_repos = Enum.map(repositories, &Repo.preload(&1, :files, force: true))
      send_to_receiver(receiver_pid, component_name, {:content_refresh_complete, refreshed_repos})
      :ok
    rescue
      e ->
        Logger.error("Error in RefreshFileContentWorker: #{Exception.message(e)}")

        send_to_receiver(
          receiver_pid,
          component_name,
          {:content_refresh_error, Exception.message(e)}
        )

        {:error, Exception.message(e)}
    end
  end

  defp send_to_receiver(receiver_pid, nil, message) when is_binary(receiver_pid) do
    # Convert string PID back to actual PID and send directly
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    send(pid, message)
  end

  defp send_to_receiver(receiver_pid, component_name, message)
       when is_binary(receiver_pid) and is_binary(component_name) do
    # Convert string PID back to actual PID and send with component name
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    component_atom = String.to_existing_atom(component_name)
    send(pid, {:repo_tree, component_atom, message})
  end
end
