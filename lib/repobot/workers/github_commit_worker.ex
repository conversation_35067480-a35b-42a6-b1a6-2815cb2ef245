defmodule Repobot.Workers.GitHubCommitWorker do
  @moduledoc """
  Oban worker for handling GitHub commit operations during onboarding.

  This worker handles the async GitHub operations that were previously
  done with Task.start in the Summary step.
  """

  use Oban.Worker, queue: :github, max_attempts: 3

  require Logger

  alias Repobot.{Repo, Repositories}

  @impl Oban.Worker
  def perform(%Oban.Job{
        args: %{
          "source_files" => source_files,
          "template_repo_id" => template_repo_id,
          "user_id" => user_id,
          "receiver_pid" => receiver_pid
        }
      }) do
    # Load the template repository and user
    template_repo = Repo.get!(Repobot.Repository, template_repo_id)
    user = Repo.get!(Repobot.Accounts.User, user_id)

    Logger.info("Starting GitHub commit for template repository #{template_repo.full_name}")

    github_api = Application.get_env(:repobot, :github_api)
    client = github_api.client(template_repo.owner, template_repo.name)

    # Filter out files with nil content before creating the tree
    valid_source_files_for_tree =
      Enum.filter(source_files, &(&1["content"] != nil))

    if Enum.empty?(valid_source_files_for_tree) do
      Logger.warning(
        "No valid source files with content found to create tree for #{template_repo.full_name}"
      )

      # Send success result since no commit is needed
      send_to_receiver(receiver_pid, {:github_commit_result, :ok, source_files, template_repo})
      :ok
    else
      case commit_files_to_github(
             github_api,
             client,
             template_repo,
             valid_source_files_for_tree,
             user
           ) do
        {:ok, refreshed_template_repo} ->
          send_to_receiver(
            receiver_pid,
            {:github_commit_result, :ok, source_files, refreshed_template_repo}
          )

          :ok

        {:error, reason} ->
          Logger.error("Failed to commit files to GitHub: #{inspect(reason)}")

          send_to_receiver(
            receiver_pid,
            {:github_commit_result, {:error, reason}, source_files, template_repo}
          )

          {:error, reason}
      end
    end
  end

  defp commit_files_to_github(github_api, client, template_repo, source_files, user) do
    # Create tree entries for each source file
    tree_entries =
      Enum.map(source_files, fn source_file ->
        %{
          "path" => source_file["target_path"],
          "mode" => "100644",
          "type" => "blob",
          "content" => source_file["content"]
        }
      end)

    # Get the current commit SHA for the default branch
    case github_api.get_ref(
           client,
           template_repo.owner,
           template_repo.name,
           "heads/#{template_repo.data["default_branch"]}"
         ) do
      {200, %{"object" => %{"sha" => parent_sha}}, _} ->
        # Create a new tree
        case github_api.create_tree(
               client,
               template_repo.owner,
               template_repo.name,
               tree_entries,
               parent_sha
             ) do
          {201, %{"sha" => new_tree_sha}, _} ->
            # Create a commit message
            commit_message =
              "Add template files from onboarding\n\nAdded #{length(source_files)} template files."

            # Create the commit
            case github_api.create_commit(
                   client,
                   template_repo.owner,
                   template_repo.name,
                   commit_message,
                   new_tree_sha,
                   [parent_sha]
                 ) do
              {201, %{"sha" => commit_sha}, _} ->
                # Update the reference
                case github_api.update_reference(
                       client,
                       template_repo.owner,
                       template_repo.name,
                       "heads/#{template_repo.data["default_branch"]}",
                       commit_sha,
                       false
                     ) do
                  {200, _, _} ->
                    # Refresh the template repository files in the database
                    case Repositories.refresh_repository_files!(template_repo.id, user) do
                      {:ok, refreshed_template_repo} ->
                        {:ok, refreshed_template_repo}

                      {:error, refresh_error} ->
                        Logger.error(
                          "Failed to refresh template repository files after GitHub commit: #{inspect(refresh_error)}"
                        )

                        # Return original repo but log the refresh error
                        {:ok, template_repo}
                    end

                  {status, body, _} ->
                    {:error, "Failed to update ref: #{status} - #{inspect(body)}"}
                end

              {status, body, _} ->
                {:error, "Failed to create commit: #{status} - #{inspect(body)}"}
            end

          {status, body, _} ->
            {:error, "Failed to create tree: #{status} - #{inspect(body)}"}
        end

      {status, body, _} ->
        {:error, "Failed to get reference: #{status} - #{inspect(body)}"}
    end
  end

  defp send_to_receiver(receiver_pid, message) when is_binary(receiver_pid) do
    # Convert string PID back to actual PID and send message
    pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
    send(pid, message)
  end
end
