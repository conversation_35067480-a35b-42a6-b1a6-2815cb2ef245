defmodule Repobot.Files.RepoTree do
  @moduledoc """
  Handles loading and tracking state of repository file trees.
  """

  require Logger

  alias <PERSON>ob<PERSON>.{Repositories, Repo}

  @type loading_state :: :not_started | :loading | :loaded | {:error, String.t()}
  @type repo_id :: binary()
  @type loading_states :: %{repo_id() => loading_state()}
  @type progress :: integer()

  @doc """
  Initializes loading states for the given repositories.
  Returns {loading_states, repositories_needing_refresh}.
  """
  @spec init_loading([Repobot.Repository.t()]) :: {loading_states(), [Repobot.Repository.t()]}
  def init_loading(repositories) do
    repositories
    |> Enum.reduce({%{}, []}, fn repo, {states, refresh_list} ->
      repo = Repo.preload(repo, :files)

      if Enum.empty?(repo.files) do
        {Map.put(states, repo.id, :loading), [repo | refresh_list]}
      else
        {Map.put(states, repo.id, :loaded), refresh_list}
      end
    end)
  end

  @doc """
  Starts loading trees for repositories that need refresh.
  Takes a list of repositories and a receiver to send state updates to.

  The receiver can be either:
  - A PID: Messages will be sent directly to this process
  - A tuple {pid, component_name}: Messages will be sent to the PID with component_name included
    to allow routing to the appropriate component

  ## Messages sent to receiver:
  - {:tree_loaded, repo_id} - When a repository's tree is loaded
  - {:tree_load_failed, repo_id, reason} - When loading fails

  When using {pid, component_name} format, messages will be:
  - {:repo_tree, component_name, {:tree_loaded, repo_id}}
  - {:repo_tree, component_name, {:tree_load_failed, repo_id, reason}}
  """
  @spec load_trees([Repobot.Repository.t()], pid() | {pid(), atom()}, Repobot.Accounts.User.t()) ::
          :ok
  def load_trees(repositories, receiver, user) do
    Enum.each(repositories, fn repo ->
      job_args = build_load_tree_job_args(repo, receiver, user)

      Repobot.Workers.LoadRepositoryTreeWorker.new(job_args)
      |> Oban.insert()
    end)

    :ok
  end

  @doc """
  Updates loading states based on received message.
  Returns updated loading states map and whether all trees are loaded.
  """
  @spec handle_tree_message(loading_states(), term()) ::
          {loading_states(), boolean()}
  def handle_tree_message(loading_states, message) do
    case message do
      {:tree_loaded, repo_id} ->
        new_states = Map.put(loading_states, repo_id, :loaded)
        {new_states, all_trees_loaded?(new_states)}

      {:tree_load_failed, repo_id, reason} ->
        new_states = Map.put(loading_states, repo_id, {:error, reason})
        {new_states, false}

      _ ->
        {loading_states, false}
    end
  end

  @doc """
  Checks if all trees are loaded in the given loading states.
  """
  @spec all_trees_loaded?(loading_states()) :: boolean()
  def all_trees_loaded?(loading_states) do
    Enum.all?(loading_states, fn {_repo_id, state} -> state == :loaded end)
  end

  @doc """
  Gets the loading state for a specific repository.
  """
  @spec get_loading_state(loading_states(), repo_id()) :: loading_state()
  def get_loading_state(loading_states, repo_id) do
    Map.get(loading_states, repo_id, :not_started)
  end

  @doc """
  Checks if any trees are still loading.
  """
  @spec loading?(loading_states()) :: boolean()
  def loading?(loading_states) do
    Enum.any?(loading_states, fn {_repo_id, state} -> state == :loading end)
  end

  @doc """
  Gets list of repository IDs that failed to load.
  """
  @spec failed_repos(loading_states()) :: [{repo_id(), String.t()}]
  def failed_repos(loading_states) do
    loading_states
    |> Enum.filter(fn {_repo_id, state} -> match?({:error, _}, state) end)
    |> Enum.map(fn {repo_id, {:error, reason}} -> {repo_id, reason} end)
  end

  @doc """
  Refreshes content for all files in the given repositories.
  Takes a list of repositories and a receiver to send progress updates to.

  The receiver can be either:
  - A PID: Messages will be sent directly to this process
  - A tuple {pid, component_name}: Messages will be sent to the PID with component_name included
    to allow routing to the appropriate component

  ## Messages sent to receiver:
  - {:content_refresh_progress, progress, status} - Progress from 0 to 100 with status message
  - {:content_refresh_complete, refreshed_repos} - When all content is refreshed
  - {:content_refresh_error, reason} - If refresh fails

  When using {pid, component_name} format, messages will be:
  - {:repo_tree, component_name, {:content_refresh_progress, progress, status}}
  - {:repo_tree, component_name, {:content_refresh_complete, refreshed_repos}}
  - {:repo_tree, component_name, {:content_refresh_error, reason}}
  """
  @spec refresh_file_content(
          [Repobot.Repository.t()],
          pid() | {pid(), atom()},
          Repobot.Accounts.User.t()
        ) :: :ok
  def refresh_file_content(repositories, receiver, user) do
    # Generate a unique job ID for tracking this batch
    job_id = :crypto.strong_rand_bytes(16) |> Base.encode64()

    job_args = build_refresh_content_job_args(repositories, receiver, user, job_id)

    Repobot.Workers.RefreshFileContentWorker.new(job_args)
    |> Oban.insert()

    :ok
  end

  defp build_load_tree_job_args(repo, receiver, user) do
    case receiver do
      {pid, component_name} when is_pid(pid) and is_atom(component_name) ->
        %{
          "repository_id" => repo.id,
          "user_id" => user.id,
          "receiver_pid" => Base.encode64(:erlang.term_to_binary(pid)),
          "component_name" => Atom.to_string(component_name)
        }

      pid when is_pid(pid) ->
        %{
          "repository_id" => repo.id,
          "user_id" => user.id,
          "receiver_pid" => Base.encode64(:erlang.term_to_binary(pid))
        }
    end
  end

  defp build_refresh_content_job_args(repositories, receiver, user, job_id) do
    repository_ids = Enum.map(repositories, & &1.id)

    case receiver do
      {pid, component_name} when is_pid(pid) and is_atom(component_name) ->
        %{
          "repository_ids" => repository_ids,
          "user_id" => user.id,
          "receiver_pid" => Base.encode64(:erlang.term_to_binary(pid)),
          "component_name" => Atom.to_string(component_name),
          "job_id" => job_id
        }

      pid when is_pid(pid) ->
        %{
          "repository_ids" => repository_ids,
          "user_id" => user.id,
          "receiver_pid" => Base.encode64(:erlang.term_to_binary(pid)),
          "job_id" => job_id
        }
    end
  end

  defp send_to_receiver(receiver_pid, message) when is_pid(receiver_pid) do
    send(receiver_pid, message)
  end

  defp send_to_receiver({pid, component_name}, message)
       when is_pid(pid) and is_atom(component_name) do
    send(pid, {:repo_tree, component_name, message})
  end
end
